name: Pull Request Checks

on:
  pull_request:
    branches: [main, develop]
    paths:
      - 'backend/**'
      - 'frontend/**'
      - '.github/workflows/**'
      - 'docker-compose*.yml'
      - 'Dockerfile*'
  push:
    branches: [main, develop]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Backend Testing
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'
          cache-dependency-path: 'backend/requirements.txt'

      - name: Install dependencies
        run: |
          cd backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest-cov pytest-xdist

      - name: Set up environment variables
        run: |
          cd backend
          cat > .env << EOF
          DATABASE_URL=postgresql+asyncpg://testuser:testpass@localhost:5432/testdb
          TEST_DATABASE_URL=postgresql+asyncpg://testuser:testpass@localhost:5432/testdb
          REDIS_URL=redis://localhost:6379
          SECRET_KEY=test-secret-key-for-ci-1234567890abcdef
          JWT_SECRET_KEY=test-jwt-secret-key-for-ci-1234567890abcdef
          JWT_ALGORITHM=HS256
          JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
          JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
          ENVIRONMENT=testing
          APP_NAME=Kuroibara
          APP_ENV=testing
          APP_DEBUG=false
          APP_URL=http://localhost:8000
          ALLOWED_HOSTS=localhost,127.0.0.1
          CORS_ORIGINS=http://localhost:3000,http://localhost:8080
          STORAGE_TYPE=local
          STORAGE_PATH=/tmp/storage
          VALKEY_HOST=localhost
          VALKEY_PORT=6379
          VALKEY_DB=0
          EOF

      - name: Initialize database and run migrations
        run: |
          cd backend
          # Initialize the database with base schema
          python init_test_db.py
          # Then run any additional migrations
          alembic upgrade head
        env:
          DATABASE_URL: postgresql+asyncpg://testuser:testpass@localhost:5432/testdb
          SECRET_KEY: test-secret-key-for-ci-1234567890abcdef
          JWT_SECRET_KEY: test-jwt-secret-key-for-ci-1234567890abcdef
          JWT_ALGORITHM: HS256

      - name: Run tests with coverage
        run: |
          cd backend
          pytest -v --cov=app --cov-report=xml --cov-report=html --cov-report=term-missing tests/
        env:
          DATABASE_URL: postgresql+asyncpg://testuser:testpass@localhost:5432/testdb
          TEST_DATABASE_URL: postgresql+asyncpg://testuser:testpass@localhost:5432/testdb
          SECRET_KEY: test-secret-key-for-ci-1234567890abcdef
          JWT_SECRET_KEY: test-jwt-secret-key-for-ci-1234567890abcdef
          JWT_ALGORITHM: HS256

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        if: always()
        with:
          file: ./backend/coverage.xml
          flags: backend
          name: backend-coverage
          fail_ci_if_error: false

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: backend-test-results
          path: |
            backend/htmlcov/
            backend/coverage.xml

  # Frontend Testing
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: 'frontend/app/package-lock.json'

      - name: Install dependencies
        run: |
          cd frontend/app
          npm ci

      - name: Run linting
        run: |
          cd frontend/app
          npm run lint

      - name: Run type checking
        run: |
          cd frontend/app
          npm run type-check

      - name: Run tests
        run: |
          cd frontend/app
          npm run test:run

      - name: Run tests with coverage
        run: |
          cd frontend/app
          npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        if: always()
        with:
          file: ./frontend/app/coverage/clover.xml
          flags: frontend
          name: frontend-coverage
          fail_ci_if_error: false

  # Security Scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install backend dependencies
        run: |
          cd backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Install frontend dependencies
        run: |
          cd frontend/app
          npm install

      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/setup@master

      - name: Snyk Code test (SAST)
        run: snyk code test --sarif-file-output=snyk-code.sarif
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        continue-on-error: true

      - name: Snyk Open Source test - Backend
        run: |
          cd backend
          snyk test --file=requirements.txt --package-manager=pip --sarif-file-output=../snyk-backend-deps.sarif
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        continue-on-error: true

      - name: Snyk Open Source test - Frontend
        run: |
          cd frontend/app
          snyk test --file=package.json --sarif-file-output=../../snyk-frontend-deps.sarif
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        continue-on-error: true

      - name: Check for SARIF files
        run: |
          echo "Checking for SARIF files..."
          ls -la *.sarif || echo "No SARIF files found"

      - name: Upload Snyk SARIF results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        continue-on-error: true
        with:
          sarif_file: |
            snyk-code.sarif
            snyk-backend-deps.sarif
            snyk-frontend-deps.sarif

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        continue-on-error: true
        with:
          sarif_file: 'trivy-results.sarif'

  # Docker Build Test
  docker-build:
    name: Docker Build Test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: false
          tags: kuroibara-backend:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: false
          tags: kuroibara-frontend:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Compose
        run: |
          # Create test environment file for Docker Compose network
          cat > .env << EOF
          # Database configuration for Docker Compose network
          DB_HOST=postgres
          DB_PORT=5432
          DB_DATABASE=testdb
          DB_USERNAME=testuser
          DB_PASSWORD=testpass
          DB_CONNECTION=postgresql+asyncpg
          DATABASE_URL=postgresql+asyncpg://testuser:testpass@postgres:5432/testdb
          TEST_DATABASE_URL=postgresql+asyncpg://testuser:testpass@postgres:5432/test_testdb

          # PostgreSQL service environment
          POSTGRES_PASSWORD=testpass
          POSTGRES_USER=testuser
          POSTGRES_DB=testdb

          # JWT and Security
          SECRET_KEY=test-secret-key-for-integration
          JWT_SECRET_KEY=test-jwt-secret-key-for-integration
          JWT_ALGORITHM=HS256
          JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
          JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

          # Valkey (Redis) configuration for Docker Compose network
          VALKEY_HOST=valkey
          VALKEY_PORT=6379
          VALKEY_DB=0

          # Application settings
          APP_NAME=Kuroibara
          APP_ENV=testing
          APP_DEBUG=false
          APP_URL=http://localhost:8000
          ENVIRONMENT=testing
          ALLOWED_HOSTS=localhost,127.0.0.1
          CORS_ORIGINS=http://localhost:3000,http://localhost:8080

          # Storage
          STORAGE_TYPE=local
          STORAGE_PATH=/app/storage

          # Disable features for testing
          ENABLE_DB_INIT=false
          ENABLE_PROVIDER_MONITORING=false
          EOF

      - name: Run integration tests
        run: |
          # Start services
          docker compose -f docker-compose.yml up -d postgres valkey

          # Wait for services to be ready
          sleep 20

          # Create test database
          echo "=== Creating test database ==="
          docker compose -f docker-compose.yml exec -T postgres psql -U testuser -d testdb -c "CREATE DATABASE test_testdb;" || echo "Test database might already exist"

          # Debug: Check what's in the container
          echo "=== Container contents ==="
          docker compose -f docker-compose.yml run --rm backend ls -la
          echo "=== Tests directory ==="
          docker compose -f docker-compose.yml run --rm backend ls -la tests/
          echo "=== Running pytest ==="

          # Run integration tests with explicit path
          docker compose -f docker-compose.yml run --rm backend pytest tests/ -v -k "not slow" --tb=short

      - name: Cleanup
        if: always()
        run: |
          docker compose -f docker-compose.yml down -v

  # Code Quality Checks
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Python quality tools
        run: |
          pip install black isort flake8 mypy

      - name: Check Python code formatting
        run: |
          cd backend
          black --check --diff .
          isort --check-only --diff .
          flake8 .

      - name: Run type checking
        run: |
          cd backend
          mypy app/ --ignore-missing-imports || true

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install frontend dependencies
        run: |
          cd frontend/app
          npm ci

      - name: Check frontend code formatting
        run: |
          cd frontend/app
          npm run format -- --check

  # Summary Job
  pr-checks-summary:
    name: PR Checks Summary
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, security-scan, docker-build, integration-tests, code-quality]
    if: always()
    
    steps:
      - name: Check all jobs status
        run: |
          if [[ "${{ needs.backend-tests.result }}" == "success" && \
                "${{ needs.frontend-tests.result }}" == "success" && \
                "${{ needs.security-scan.result }}" == "success" && \
                "${{ needs.docker-build.result }}" == "success" && \
                "${{ needs.integration-tests.result }}" == "success" && \
                "${{ needs.code-quality.result }}" == "success" ]]; then
            echo "✅ All checks passed!"
            exit 0
          else
            echo "❌ Some checks failed:"
            echo "Backend Tests: ${{ needs.backend-tests.result }}"
            echo "Frontend Tests: ${{ needs.frontend-tests.result }}"
            echo "Security Scan: ${{ needs.security-scan.result }}"
            echo "Docker Build: ${{ needs.docker-build.result }}"
            echo "Integration Tests: ${{ needs.integration-tests.result }}"
            echo "Code Quality: ${{ needs.code-quality.result }}"
            exit 1
          fi
