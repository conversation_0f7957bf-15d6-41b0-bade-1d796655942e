# 📚 Technical Documentation

This directory contains **technical documentation** for developers and system administrators. For **user guides and tutorials**, visit our [📖 GitHub Wiki](https://github.com/Futs/kuroibara/wiki).

## 🎯 Documentation Structure

### 🔧 **Repository Docs** (Technical/Developer)
- **API References** - Technical API documentation
- **Development Guides** - Setup, contributing, architecture
- **System Administration** - Deployment, configuration, automation
- **Code Documentation** - In-depth technical details

### 📖 **GitHub Wiki** (User-Facing)
- **User Guides** - How to use Kuroibara features
- **Installation Tutorials** - Step-by-step setup guides
- **Troubleshooting** - Common issues and solutions
- **Community Content** - Tips, tricks, and best practices

---

## 📋 Technical Documentation Index

### 🏗️ **Development & Architecture**
- **[Development Guide](DEVELOPMENT.md)** - Development environment setup and guidelines
- **[API Reference](API_REFERENCE.md)** - Complete API documentation
- **[Tech Stack](TECH_STACK.md)** - Technology choices and architecture decisions
- **[Providers System](PROVIDERS.md)** - Provider architecture and adding new providers

### ⚙️ **System Administration**
- **[Configuration Guide](CONFIGURATION.md)** - Environment variables and system configuration
- **[Backup System](BACKUP_SYSTEM.md)** - Backup and recovery procedures
- **[Organizer System](ORGANIZER_SYSTEM.md)** - File organization and management

### 🤖 **Automation & CI/CD**
- **[CI/CD Setup](SETUP_CI_CD.md)** - GitHub Actions pipeline configuration
- **[Automation Setup](AUTOMATION_SETUP.md)** - Development automation tools
- **[Docker Automation](DOCKER_AUTOMATION.md)** - Container automation and deployment
- **[Git Automation](GIT_AUTOMATION.md)** - Git hooks and automated workflows

### 📊 **Project Management**
- **[Git Guidelines](GIT_GUIDELINES.md)** - Git workflow and contribution standards
- **[Roadmap](ROADMAP.md)** - Project roadmap and planned features
- **[Versioning Guide](../VERSIONING.md)** - Semantic versioning and release management
- **[Changelog](../CHANGELOG.md)** - Detailed version history

### 🔗 **API Documentation**
- **[API Organizer](API_ORGANIZER.md)** - Organizer API endpoints
- **[Live API Docs](http://localhost:8000/api/docs)** - Interactive API documentation (local)

---

## 🚀 **Quick Start for Developers**

1. **📖 New to Kuroibara?** → Start with the [GitHub Wiki](https://github.com/Futs/kuroibara/wiki)
2. **🔧 Setting up development?** → Read [Development Guide](DEVELOPMENT.md)
3. **🤝 Want to contribute?** → Check [Git Guidelines](GIT_GUIDELINES.md)
4. **🐛 Found a bug?** → See [Troubleshooting](https://github.com/Futs/kuroibara/wiki/Troubleshooting) in the wiki

## 🔗 **External Documentation**

- **[📖 User Wiki](https://github.com/Futs/kuroibara/wiki)** - User guides and tutorials
- **[🏠 Main README](../README.md)** - Project overview and quick start
- **[🔧 Backend Docs](../backend/README.md)** - Backend-specific documentation
- **[🎨 Frontend Docs](../frontend/README.md)** - Frontend-specific documentation

---

## 🤝 **Contributing to Documentation**

### **Technical Documentation** (This Repository)
- Submit pull requests for technical docs
- Follow the established structure and formatting
- Include code examples and technical details

### **User Documentation** (GitHub Wiki)
- Edit wiki pages directly on GitHub
- Focus on user-friendly language and step-by-step guides
- Include screenshots and visual aids when helpful

---

## 📞 **Getting Help**

- **🐛 Technical Issues**: Open an issue on GitHub
- **❓ Usage Questions**: Check the [Wiki](https://github.com/Futs/kuroibara/wiki) or start a discussion
- **💬 Community**: Join discussions in GitHub Discussions
