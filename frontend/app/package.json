{"name": "app", "private": true, "version": "0.2.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --fix", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "axios": "^1.9.0", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^2.1.9", "@tailwindcss/postcss": "^4.0.0", "@vitest/ui": "^2.1.8", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.15", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.31.0", "jsdom": "^25.0.1", "postcss": "^8.4.29", "prettier": "^3.3.3", "tailwindcss": "^4.0.0", "typescript": "^5.6.3", "vite": "^6.3.5", "vitest": "^2.1.8", "vue-tsc": "^2.1.10"}}