{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "noEmit": true, "skipLibCheck": true, "allowJs": true, "checkJs": false, "strict": false, "noImplicitAny": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "src/**/*.vue", "src/**/*.js"]}