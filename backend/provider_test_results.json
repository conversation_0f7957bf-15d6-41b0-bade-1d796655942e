{"summary": {"total_providers": 80, "working_providers": 7, "partial_providers": 2, "broken_providers": 0, "test_timestamp": **********.7366521}, "detailed_results": [{"provider_id": "mangadex", "provider_name": "MangaDex", "provider_url": "https://api.mangadex.org", "class_name": "MangaDexProvider", "supports_nsfw": true, "timestamp": **********.7385445, "url_test": {"accessible": true, "status_code": 200, "response_time": 1.3, "error": null, "content_type": "text/html; charset=utf-8", "redirected_url": "https://api.mangadex.org/docs/"}, "search_test": {"search_works": true, "results_count": 5, "has_results": true, "error": null, "sample_result": {"id": "a787b10a-02d0-46c0-8236-0d01d69ad4a3", "title": "<PERSON><PERSON><PERSON> (Official Colored)", "cover_image": "https://uploads.mangadex.org/covers/a787b10a-02d0-46c0-8236-0d01d69ad4a3/a2b399c6-a938-4971-89f3-870b30038e5a.jpg", "description": "Twelve years ago, the powerful Nine-Tailed Demon Fox attacked the ninja village of Konohagakure. The demon was quickly defeated and sealed into the infant <PERSON><PERSON><PERSON>, by the Fourth Hokage who sacrificed his life to protect the village. Now <PERSON><PERSON><PERSON> is the number one knucklehead ninja who's determined to become the next Hokage and gain recognition by everyone who ever doubted him!"}, "total_available": 131, "has_more_pages": true}, "metadata_test": {"metadata_works": true, "has_title": true, "has_description": true, "has_cover": true, "has_genres": false, "error": null, "metadata_sample": {"title": "Chainsaw Man", "description": "Broke young man + chainsaw dog demon = Chainsaw Man!  \n  \nThe name says it all! <PERSON><PERSON>'s life of poverty is changed forever when he merges with his pet chainsaw dog, <PERSON><PERSON><PERSON>! Now he's living in the big", "cover_image": "https://uploads.mangadex.org/covers/a77742b1-befd-49a4-bff5-1ad4e6b0ef7b/fdf0d950-c72f-405b-9222-8b32a1f6e5ae.jpg", "genres": []}}, "cover_image_test": {"accessible": false, "status_code": 405, "content_type": "text/plain; charset=utf-8", "content_length": "14", "error": null}, "overall_status": "working"}, {"provider_id": "mangaplus", "provider_name": "MangaPlus", "provider_url": "https://jumpg-webapi.tokyo-cdn.com/api", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangasee", "provider_name": "MangaSee", "provider_url": "https://mangasee123.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "arcanescans", "provider_name": "ArcaneScans", "provider_url": "https://arcanescans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "hentairead", "provider_name": "HentaiRead", "provider_url": "https://hentairead.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "radiantscans", "provider_name": "RadiantScans", "provider_url": "https://radiantscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "sectscans", "provider_name": "SectScans", "provider_url": "https://sectscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manga3s", "provider_name": "Manga3S", "provider_url": "https://manga3s.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "vizshonenJump", "provider_name": "VizShonenJump", "provider_url": "https://www.viz.com/shonenjump", "class_name": "GenericProvider", "supports_nsfw": false, "timestamp": **********.7826076, "url_test": {"accessible": true, "status_code": 200, "response_time": 0.2, "error": null, "content_type": "text/html; charset=utf-8", "redirected_url": null}, "search_test": {}, "metadata_test": {}, "cover_image_test": {}, "overall_status": "partial"}, {"provider_id": "man<PERSON><PERSON>", "provider_name": "ManhuaUs", "provider_url": "https://manhuaus.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangahentai", "provider_name": "MangaHentai", "provider_url": "https://mangahentai.me", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "theguildscans", "provider_name": "TheGuildScans", "provider_url": "https://theguildscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "freeman<PERSON>", "provider_name": "FreeManga", "provider_url": "https://freemanga.me", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manhuaplus", "provider_name": "ManhuaPlus", "provider_url": "https://manhuaplus.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "reaperscans", "provider_name": "ReaperScans", "provider_url": "https://reaperscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "hentaiwebtoon", "provider_name": "HentaiWebtoon", "provider_url": "https://hentaiwebtoon.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangaread", "provider_name": "MangaRead", "provider_url": "https://mangaread.org", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manytooncom", "provider_name": "ManyToonCOM", "provider_url": "https://manytoon.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manhuafast", "provider_name": "ManhuaFast", "provider_url": "https://manhuafast.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manganel", "provider_name": "MangaNel", "provider_url": "https://manganel.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "weebcentral", "provider_name": "WeebCentral", "provider_url": "https://weebcentral.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "anigliscans", "provider_name": "AniGliScans", "provider_url": "https://anigliscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "eightmusesxxx", "provider_name": "EightMusesXXX", "provider_url": "https://8muses.xxx", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "provider_name": "ManhwaHentaiMe", "provider_url": "https://manhwahentai.me", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangalife", "provider_name": "MangaLife", "provider_url": "https://manga4life.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangareaderto", "provider_name": "MangaReaderTo", "provider_url": "https://mangareader.to", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangabat", "provider_name": "MangaBat", "provider_url": "https://mangabats.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "<PERSON>hen<PERSON>", "provider_name": "IMHentai", "provider_url": "https://imhentai.xxx", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "monochromescans", "provider_name": "MonochromeScans", "provider_url": "https://monochromescans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "taptrans", "provider_name": "TapTrans", "provider_url": "https://taptrans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangafire", "provider_name": "MangaFire", "provider_url": "https://mangafire.to", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "toonily", "provider_name": "<PERSON><PERSON><PERSON>", "provider_url": "https://toonily.com", "class_name": "GenericProvider", "supports_nsfw": true, "timestamp": **********.0191636, "url_test": {"accessible": true, "status_code": 200, "response_time": 0.92, "error": null, "content_type": "text/html; charset=UTF-8", "redirected_url": null}, "search_test": {"search_works": true, "results_count": 18, "has_results": true, "error": null, "sample_result": {"id": "black-crow", "title": "Black Crow", "cover_image": "https://toonily.com/wp-content/themes/toonily/images/dflazy.jpg", "description": "Black Crow\n\n\n\n\nBlack Crow\n 45   \n 240.6K"}, "total_available": 18, "has_more_pages": true}, "metadata_test": {"metadata_works": true, "has_title": false, "has_description": false, "has_cover": false, "has_genres": false, "error": null, "metadata_sample": {"title": "", "description": "", "cover_image": "", "genres": []}}, "cover_image_test": {}, "overall_status": "working"}, {"provider_id": "readm", "provider_name": "ReadM", "provider_url": "https://readm.org", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "<PERSON><PERSON><PERSON>", "provider_name": "MangaH<PERSON><PERSON>", "provider_url": "https://mangahasu.se", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangatown", "provider_name": "MangaTown", "provider_url": "https://mangatown.com", "class_name": "GenericProvider", "supports_nsfw": false, "timestamp": **********.9041932, "url_test": {"accessible": true, "status_code": 200, "response_time": 1.42, "error": null, "content_type": "text/html; charset=utf-8", "redirected_url": "http://www.mangatown.com/"}, "search_test": {"search_works": true, "results_count": 2, "has_results": true, "error": null, "sample_result": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>_wa_boku_no_hero", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa <PERSON>ku no Hero!", "cover_image": "http://fmcdn.mangahere.com/store/manga/30031/ocover.jpg?token=5c50830b02253d774358730d0739b3da6a53a273&ttl=**********&v=0", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa <PERSON>ku no Hero!"}, "total_available": 2, "has_more_pages": true}, "metadata_test": {"metadata_works": true, "has_title": false, "has_description": false, "has_cover": false, "has_genres": false, "error": null, "metadata_sample": {"title": "", "description": "", "cover_image": "", "genres": []}}, "cover_image_test": {}, "overall_status": "working"}, {"provider_id": "mangaheretoday", "provider_name": "MangaHereToday", "provider_url": "https://mangahere.today", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "webtoontrcom", "provider_name": "WebtoonTRCOM", "provider_url": "https://webtoon.tr.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangakakalot", "provider_name": "MangaKakalot", "provider_url": "https://mangakakalot.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "xunscans", "provider_name": "XunScans", "provider_url": "https://xunscans.xyz", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "novelcoolen", "provider_name": "NovelCoolEN", "provider_url": "https://novelcool.com/en", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangapill", "provider_name": "MangaPill", "provider_url": "https://mangapill.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "anshscans", "provider_name": "AnshScans", "provider_url": "https://anshscans.org", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "xlecx", "provider_name": "XlecX", "provider_url": "https://xlecx.org", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "eightmuses", "provider_name": "EightMuses", "provider_url": "https://8muses.io", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manganelos", "provider_name": "<PERSON><PERSON><PERSON><PERSON>", "provider_url": "https://manganelos.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "henta<PERSON>xus", "provider_name": "HentaiNexus", "provider_url": "https://hentainexus.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "<PERSON><PERSON><PERSON><PERSON>", "provider_name": "<PERSON><PERSON><PERSON><PERSON>", "provider_url": "https://manhwahub.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "lew<PERSON><PERSON>", "provider_name": "LewdManhwa", "provider_url": "https://lewdmanhwa.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangarockteam", "provider_name": "MangaRockTeam", "provider_url": "https://mangarockteam.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "deathtollscans", "provider_name": "DeathTollScans", "provider_url": "https://deathtollscans.net", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "<PERSON><PERSON><PERSON>", "provider_name": "MangaOwlio", "provider_url": "https://mangaowl.io", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "firstkiss", "provider_name": "FirstKiss", "provider_url": "https://1stkiss.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "woopread", "provider_name": "WoopRead", "provider_url": "https://woopread.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "tsu<PERSON>o", "provider_name": "<PERSON><PERSON><PERSON><PERSON>", "provider_url": "https://tsumino.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "dynastyscans", "provider_name": "DynastyScans", "provider_url": "https://dynasty-scans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "webcomicsapp", "provider_name": "WebComicsApp", "provider_url": "https://www.webcomicsapp.com", "class_name": "GenericProvider", "supports_nsfw": false, "timestamp": **********.295144, "url_test": {"accessible": true, "status_code": 200, "response_time": 0.05, "error": null, "content_type": "text/html; charset=utf-8", "redirected_url": null}, "search_test": {"search_works": true, "results_count": 2, "has_results": true, "error": null, "sample_result": {"id": "654afede8c252b6aab3f1664", "title": "Recommend", "cover_image": "https://www.webcomicsapp.com/_nuxt/img/empty.e635de8.png", "description": "No word found for"}, "total_available": 2, "has_more_pages": false}, "metadata_test": {"metadata_works": true, "has_title": false, "has_description": false, "has_cover": false, "has_genres": false, "error": null, "metadata_sample": {"title": "", "description": "", "cover_image": "", "genres": []}}, "cover_image_test": {}, "overall_status": "working"}, {"provider_id": "vortexscans", "provider_name": "VortexScans", "provider_url": "https://vortexscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "omegascans", "provider_name": "OmegaScans", "provider_url": "https://omegascans.org", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "tcbscans", "provider_name": "TCBScans", "provider_url": "https://tcbscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "culturedworks", "provider_name": "CulturedWorks", "provider_url": "https://culturedworks.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "readallcomics", "provider_name": "ReadAllComics", "provider_url": "https://readallcomics.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "wordexcerpt", "provider_name": "WordExcerpt", "provider_url": "https://wordexcerpt.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manganelotoday", "provider_name": "MangaNeloToday", "provider_url": "https://manganelo.today", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "kisscomic", "provider_name": "KissComic", "provider_url": "https://kisscomic.io", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "<PERSON><PERSON><PERSON>", "provider_name": "MangaKisa", "provider_url": "https://mangakisa.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "whimsubs", "provider_name": "Whim<PERSON><PERSON>s", "provider_url": "https://whimsubs.xyz", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "tenshimoe", "provider_name": "TenshiMoe", "provider_url": "https://tenshi.moe", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "myanimelist", "provider_name": "MyAnimeListManga", "provider_url": "https://myanimelist.net", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "mangabuddy", "provider_name": "MangaBudd<PERSON>", "provider_url": "https://mangabuddy.com", "class_name": "GenericProvider", "supports_nsfw": false, "timestamp": **********.4964228, "url_test": {"accessible": true, "status_code": 200, "response_time": 0.99, "error": null, "content_type": "text/html; charset=utf-8", "redirected_url": "https://mangabuddy.com/official"}, "search_test": {"search_works": true, "results_count": 1, "has_results": true, "error": null, "sample_result": {"id": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "cover_image": "https://mangabuddy.com/static/common/x.gif", "description": "Read Naruto Manga Online Naruto is a manga series from Japan. It's about the story of a young ninja who wants to become the strongest leader in his village. The series were produced by <PERSON><PERSON><PERSON> and in 1997 were published. Later this manga wa"}, "total_available": 1, "has_more_pages": false}, "metadata_test": {"metadata_works": true, "has_title": false, "has_description": false, "has_cover": false, "has_genres": false, "error": null, "metadata_sample": {"title": "", "description": "", "cover_image": "", "genres": []}}, "cover_image_test": {}, "overall_status": "working"}, {"provider_id": "manga18fx", "provider_name": "Manga18FX", "provider_url": "https://manga18fx.com", "class_name": "GenericProvider", "supports_nsfw": true, "timestamp": **********.3277402, "url_test": {"accessible": true, "status_code": 200, "response_time": 0.97, "error": null, "content_type": "text/html; charset=UTF-8", "redirected_url": null}, "search_test": {"search_works": true, "results_count": 1, "has_results": true, "error": null, "sample_result": {"id": "privacy-policy", "title": "Privacy Policy", "cover_image": "", "description": "Copyrights and trademarks for the manga, and other promotional materials are held by their respective owners and their use is allowed under the fair use clause of the Copyright Law. © 2025 Manga18FX.COM"}, "total_available": 1, "has_more_pages": true}, "metadata_test": {"metadata_works": true, "has_title": false, "has_description": false, "has_cover": false, "has_genres": false, "error": null, "metadata_sample": {"title": "", "description": "", "cover_image": "", "genres": []}}, "cover_image_test": {}, "overall_status": "working"}, {"provider_id": "mangafox", "provider_name": "MangaFox", "provider_url": "https://fanfox.net", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "hatigarmscans", "provider_name": "HatigarmScans", "provider_url": "https://hatigarmscans.net", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manhuaz", "provider_name": "ManhuaZ", "provider_url": "https://manhuaz.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manhuaga", "provider_name": "Manhuaga", "provider_url": "https://manhuaga.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "manga<PERSON>a", "provider_name": "MangaDNA", "provider_url": "https://mangadna.com", "class_name": "GenericProvider", "supports_nsfw": false, "timestamp": **********.171069, "url_test": {"accessible": true, "status_code": 200, "response_time": 0.95, "error": null, "content_type": "text/html; charset=UTF-8", "redirected_url": null}, "search_test": {"search_works": true, "results_count": 2, "has_results": true, "error": null, "sample_result": {"id": "star-rating-svg.css", "title": "RESULTS FOR \"naruto\"", "cover_image": "", "description": "No result for \"naruto\""}, "total_available": 2, "has_more_pages": true}, "metadata_test": {"metadata_works": true, "has_title": false, "has_description": false, "has_cover": false, "has_genres": false, "error": null, "metadata_sample": {"title": "", "description": "", "cover_image": "", "genres": []}}, "cover_image_test": {}, "overall_status": "working"}, {"provider_id": "mangaeighteenUS", "provider_name": "MangaEighteenUS", "provider_url": "https://manga18.us", "class_name": "GenericProvider", "supports_nsfw": true, "timestamp": **********.15871, "url_test": {"accessible": true, "status_code": 200, "response_time": 0.99, "error": null, "content_type": "text/html; charset=UTF-8", "redirected_url": "https://manhuascan.us/"}, "search_test": {}, "metadata_test": {}, "cover_image_test": {}, "overall_status": "partial"}, {"provider_id": "viewcomics", "provider_name": "ViewComics", "provider_url": "https://viewcomics.me", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "coloredmanga", "provider_name": "ColoredManga", "provider_url": "https://coloredmanga.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "crazyscans", "provider_name": "CrazyScans", "provider_url": "https://crazyscans.com", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}, {"provider_id": "<PERSON><PERSON>gain", "provider_name": "KissmangaIN", "provider_url": "https://kissmanga.in", "overall_status": "error", "error": "'NoneType' object has no attribute 'get'"}]}