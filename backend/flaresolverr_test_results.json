[{"provider_name": "ReaperScans", "url": "https://reaperscans.com", "direct": {"url": "https://reaperscans.com", "method": "direct", "success": false, "status_code": 521, "error": "Cloudflare protection detected (status: 521)", "response_time": 0.29, "cloudflare_detected": true}, "flaresolverr": {"url": "https://reaperscans.com", "method": "flaresolverr", "success": true, "status_code": 200, "error": null, "response_time": 1.4, "content_length": 6711}, "flaresolverr_helped": true}, {"provider_name": "ManhuaFast", "url": "https://manhuafast.com", "direct": {"url": "https://manhuafast.com", "method": "direct", "success": false, "status_code": 403, "error": "Cloudflare protection detected (status: 403)", "response_time": 0.12, "cloudflare_detected": true}, "flaresolverr": {"url": "https://manhuafast.com", "method": "flaresolverr", "success": false, "status_code": null, "error": "FlareSolverr API returned 500", "response_time": 60.12, "content_length": null}, "flaresolverr_helped": false}, {"provider_name": "Manhuaga", "url": "https://manhuaga.com", "direct": {"url": "https://manhuaga.com", "method": "direct", "success": false, "status_code": 521, "error": "Cloudflare protection detected (status: 521)", "response_time": 0.22, "cloudflare_detected": true}, "flaresolverr": {"url": "https://manhuaga.com", "method": "flaresolverr", "success": true, "status_code": 200, "error": null, "response_time": 2.89, "content_length": 6690}, "flaresolverr_helped": true}, {"provider_name": "MangaFire", "url": "https://mangafire.to", "direct": {"url": "https://mangafire.to", "method": "direct", "success": false, "status_code": 200, "error": "Cloudflare protection detected (status: 200)", "response_time": 0.45, "cloudflare_detected": true}, "flaresolverr": {"url": "https://mangafire.to", "method": "flaresolverr", "success": false, "status_code": null, "error": "FlareSolverr API returned 500", "response_time": 60.16, "content_length": null}, "flaresolverr_helped": false}, {"provider_name": "MangaReaderTo", "url": "https://mangareader.to", "direct": {"url": "https://mangareader.to", "method": "direct", "success": true, "status_code": 200, "error": null, "response_time": 0.4, "cloudflare_detected": false}, "flaresolverr": {"url": "https://mangareader.to", "method": "flaresolverr", "success": false, "status_code": null, "error": "FlareSolverr API returned 500", "response_time": 60.12, "content_length": null}, "flaresolverr_helped": false}, {"provider_name": "<PERSON><PERSON><PERSON>", "url": "https://toonily.com", "direct": {"url": "https://toonily.com", "method": "direct", "success": true, "status_code": 200, "error": null, "response_time": 0.3, "cloudflare_detected": false}, "flaresolverr": {"url": "https://toonily.com", "method": "flaresolverr", "success": false, "status_code": null, "error": "FlareSolverr API returned 500", "response_time": 60.14, "content_length": null}, "flaresolverr_helped": false}]