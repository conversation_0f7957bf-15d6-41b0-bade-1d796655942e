import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

from app.core.providers.base import BaseProvider
from app.core.providers.enhanced_generic import EnhancedGenericProvider
from app.core.providers.factory import provider_factory
from app.core.providers.generic import GenericProvider
from app.core.providers.mangadex import MangaDexProvider
from app.core.providers.mangaplus import MangaPlusProvider
from app.core.providers.mangasee import MangaSeeProvider

logger = logging.getLogger(__name__)


class ProviderRegistry:
    """Registry for manga providers."""

    def __init__(self):
        self._providers: Dict[str, BaseProvider] = {}

        logger.info("Initializing ProviderRegistry")

        # Initialize provider factory
        provider_factory.register_provider_class(MangaDexProvider)
        provider_factory.register_provider_class(MangaPlusProvider)
        provider_factory.register_provider_class(MangaSeeProvider)
        provider_factory.register_provider_class(GenericProvider)
        provider_factory.register_provider_class(EnhancedGenericProvider)
        logger.info("Registered provider classes")

        # Load provider configurations
        self._load_provider_configs()

        # Register default providers if no configs were loaded
        if not self._providers:
            logger.info(
                "No providers loaded from config, registering default providers"
            )
            self.register_provider(MangaDexProvider())
            self.register_provider(MangaPlusProvider())
            self.register_provider(MangaSeeProvider())

        logger.info(
            f"ProviderRegistry initialized with {len(self._providers)} providers: {list(self._providers.keys())}"
        )

    def register_provider(self, provider: BaseProvider) -> None:
        """Register a provider."""
        self._providers[provider.name.lower()] = provider

    def get_provider(self, name: str) -> Optional[BaseProvider]:
        """Get a provider by name."""
        return self._providers.get(name.lower())

    def get_all_providers(self) -> List[BaseProvider]:
        """Get all registered providers."""
        return list(self._providers.values())

    def get_provider_names(self) -> List[str]:
        """Get the names of all registered providers."""
        return sorted([provider.name for provider in self._providers.values()])

    def get_provider_info(self) -> List[Dict[str, Any]]:
        """Get information about all registered providers."""
        # Sort providers alphabetically by name
        sorted_providers = sorted(
            self._providers.values(), key=lambda p: p.name.lower()
        )
        return [
            {
                "id": provider.name.lower(),
                "name": provider.name,
                "url": provider.url,
                "supports_nsfw": provider.supports_nsfw,
            }
            for provider in sorted_providers
        ]

    def _load_provider_configs(self) -> None:
        """Load provider configurations from JSON files."""
        try:
            # Get the directory containing provider configurations
            config_dir = Path(__file__).parent / "config"

            # Check if directory exists
            if not config_dir.exists():
                logger.warning(f"Provider config directory not found: {config_dir}")
                return

            # Check if FlareSolverr is available
            flaresolverr_url = os.getenv("FLARESOLVERR_URL")
            flaresolverr_available = bool(flaresolverr_url and flaresolverr_url.strip())

            if flaresolverr_available:
                logger.info(f"FlareSolverr available at: {flaresolverr_url}")
            else:
                logger.info("FlareSolverr not configured - Cloudflare-protected providers will be disabled")

            # Define config files to load
            config_files = ["providers_default.json"]

            # Add Cloudflare providers if FlareSolverr is available
            if flaresolverr_available:
                config_files.append("providers_cloudflare.json")
                logger.info("Loading Cloudflare-protected providers")

            # Fallback to old batch files if new structure doesn't exist
            if not (config_dir / "providers_default.json").exists():
                logger.info("Using legacy provider configuration files")
                config_files = ["providers_batch1.json", "providers_batch2.json",
                               "providers_batch3.json", "providers_batch4.json"]

            # Load specified config files
            for config_filename in config_files:
                config_file = config_dir / config_filename
                if not config_file.exists():
                    logger.warning(f"Config file not found: {config_file}")
                    continue

                try:
                    logger.info(f"Loading provider config from {config_file}")

                    # Load and filter providers based on FlareSolverr availability
                    with open(config_file, 'r') as f:
                        import json
                        provider_configs = json.load(f)

                    # Filter out Cloudflare providers if FlareSolverr is not available
                    filtered_configs = []
                    for config in provider_configs:
                        requires_flaresolverr = config.get("requires_flaresolverr", False)
                        if requires_flaresolverr and not flaresolverr_available:
                            logger.debug(f"Skipping {config.get('name', 'Unknown')} - requires FlareSolverr")
                            continue

                        # Add FlareSolverr URL to params if available and needed
                        if flaresolverr_available and config.get("params", {}).get("use_flaresolverr"):
                            config["params"]["flaresolverr_url"] = flaresolverr_url

                        filtered_configs.append(config)

                    # Load filtered configs into factory
                    provider_factory._provider_configs.update({
                        config["id"]: config for config in filtered_configs
                    })

                    # Create providers from filtered config
                    providers = []
                    for config in filtered_configs:
                        try:
                            provider = provider_factory.create_provider(config["id"])
                            if provider:
                                providers.append(provider)
                        except Exception as e:
                            logger.error(f"Failed to create provider {config.get('name', 'Unknown')}: {e}")

                    # Register providers
                    for provider in providers:
                        self.register_provider(provider)

                    logger.info(f"Loaded {len(providers)} providers from {config_file}")
                except Exception as e:
                    logger.error(f"Error loading provider config from {config_file}: {e}")

        except Exception as e:
            logger.error(f"Error loading provider configs: {e}")


# Create a singleton instance
provider_registry = ProviderRegistry()
