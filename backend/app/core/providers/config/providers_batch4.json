[{"id": "readallcomics", "name": "ReadAllComics", "class_name": "GenericProvider", "url": "https://readallcomics.com", "supports_nsfw": false, "params": {"base_url": "https://readallcomics.com", "search_url": "https://readallcomics.com/search", "manga_url_pattern": "https://readallcomics.com/comics/{manga_id}", "chapter_url_pattern": "https://readallcomics.com/comics/{manga_id}/{chapter_id}", "name": "ReadAllComics"}}, {"id": "wordexcerpt", "name": "WordExcerpt", "class_name": "GenericProvider", "url": "https://wordexcerpt.com", "supports_nsfw": false, "params": {"base_url": "https://wordexcerpt.com", "search_url": "https://wordexcerpt.com/search", "manga_url_pattern": "https://wordexcerpt.com/series/{manga_id}", "chapter_url_pattern": "https://wordexcerpt.com/series/{manga_id}/{chapter_id}", "name": "WordExcerpt"}}, {"id": "manganelotoday", "name": "MangaNeloToday", "class_name": "GenericProvider", "url": "https://manganelo.today", "supports_nsfw": false, "params": {"base_url": "https://manganelo.today", "search_url": "https://manganelo.today/search", "manga_url_pattern": "https://manganelo.today/manga/{manga_id}", "chapter_url_pattern": "https://manganelo.today/manga/{manga_id}/{chapter_id}", "name": "MangaNeloToday"}}, {"id": "kisscomic", "name": "KissComic", "class_name": "GenericProvider", "url": "https://kisscomic.io", "supports_nsfw": false, "params": {"base_url": "https://kisscomic.io", "search_url": "https://kisscomic.io/search", "manga_url_pattern": "https://kisscomic.io/comic/{manga_id}", "chapter_url_pattern": "https://kisscomic.io/comic/{manga_id}/{chapter_id}", "name": "KissComic"}}, {"id": "<PERSON><PERSON><PERSON>", "name": "MangaKisa", "class_name": "GenericProvider", "url": "https://mangakisa.com", "supports_nsfw": false, "params": {"base_url": "https://mangakisa.com", "search_url": "https://mangakisa.com/search", "manga_url_pattern": "https://mangakisa.com/manga/{manga_id}", "chapter_url_pattern": "https://mangakisa.com/manga/{manga_id}/{chapter_id}", "name": "MangaKisa"}}, {"id": "whimsubs", "name": "Whim<PERSON><PERSON>s", "class_name": "GenericProvider", "url": "https://whimsubs.xyz", "supports_nsfw": false, "params": {"base_url": "https://whimsubs.xyz", "search_url": "https://whimsubs.xyz/search", "manga_url_pattern": "https://whimsubs.xyz/manga/{manga_id}", "chapter_url_pattern": "https://whimsubs.xyz/manga/{manga_id}/{chapter_id}", "name": "Whim<PERSON><PERSON>s"}}, {"id": "tenshimoe", "name": "TenshiMoe", "class_name": "GenericProvider", "url": "https://tenshi.moe", "supports_nsfw": false, "params": {"base_url": "https://tenshi.moe", "search_url": "https://tenshi.moe/search", "manga_url_pattern": "https://tenshi.moe/manga/{manga_id}", "chapter_url_pattern": "https://tenshi.moe/manga/{manga_id}/{chapter_id}", "name": "TenshiMoe"}}, {"id": "myanimelist", "name": "MyAnimeListManga", "class_name": "GenericProvider", "url": "https://myanimelist.net", "supports_nsfw": false, "params": {"base_url": "https://myanimelist.net", "search_url": "https://myanimelist.net/manga.php?q=", "manga_url_pattern": "https://myanimelist.net/manga/{manga_id}", "chapter_url_pattern": "https://myanimelist.net/manga/{manga_id}/{chapter_id}", "name": "MyAnimeListManga"}}, {"id": "mangabuddy", "name": "MangaBudd<PERSON>", "class_name": "GenericProvider", "url": "https://mangabuddy.com", "supports_nsfw": false, "params": {"base_url": "https://mangabuddy.com", "search_url": "https://mangabuddy.com/search", "manga_url_pattern": "https://mangabuddy.com/manga/{manga_id}", "chapter_url_pattern": "https://mangabuddy.com/manga/{manga_id}/{chapter_id}", "name": "MangaBudd<PERSON>"}}, {"id": "manga18fx", "name": "Manga18FX", "class_name": "GenericProvider", "url": "https://manga18fx.com", "supports_nsfw": true, "params": {"base_url": "https://manga18fx.com", "search_url": "https://manga18fx.com/search", "manga_url_pattern": "https://manga18fx.com/manga/{manga_id}", "chapter_url_pattern": "https://manga18fx.com/manga/{manga_id}/{chapter_id}", "name": "Manga18FX"}}, {"id": "mangafox", "name": "MangaFox", "class_name": "GenericProvider", "url": "https://fanfox.net", "supports_nsfw": false, "params": {"base_url": "https://fanfox.net", "search_url": "https://fanfox.net/search?title={query}", "search_selector": "li", "search_title_selector": "a[href*='/manga/']:first-of-type", "search_cover_selector": "img", "search_description_selector": "", "manga_url_pattern": "https://fanfox.net{manga_id}", "chapter_url_pattern": "https://fanfox.net{manga_id}/{chapter_id}", "name": "MangaFox"}}, {"id": "hatigarmscans", "name": "HatigarmScans", "class_name": "GenericProvider", "url": "https://hatigarmscans.net", "supports_nsfw": false, "params": {"base_url": "https://hatigarmscans.net", "search_url": "https://hatigarmscans.net/search", "manga_url_pattern": "https://hatigarmscans.net/manga/{manga_id}", "chapter_url_pattern": "https://hatigarmscans.net/manga/{manga_id}/{chapter_id}", "name": "HatigarmScans"}}, {"id": "manhuaz", "name": "ManhuaZ", "class_name": "GenericProvider", "url": "https://manhuaz.com", "supports_nsfw": false, "params": {"base_url": "https://manhuaz.com", "search_url": "https://manhuaz.com/search", "manga_url_pattern": "https://manhuaz.com/manga/{manga_id}", "chapter_url_pattern": "https://manhuaz.com/manga/{manga_id}/{chapter_id}", "name": "ManhuaZ"}}, {"id": "manhuaga", "name": "Manhuaga", "class_name": "GenericProvider", "url": "https://manhuaga.com", "supports_nsfw": false, "params": {"base_url": "https://manhuaga.com", "search_url": "https://manhuaga.com/search", "manga_url_pattern": "https://manhuaga.com/manga/{manga_id}", "chapter_url_pattern": "https://manhuaga.com/manga/{manga_id}/{chapter_id}", "name": "Manhuaga"}}, {"id": "manga<PERSON>a", "name": "MangaDNA", "class_name": "GenericProvider", "url": "https://mangadna.com", "supports_nsfw": false, "params": {"base_url": "https://mangadna.com", "search_url": "https://mangadna.com/search", "manga_url_pattern": "https://mangadna.com/manga/{manga_id}", "chapter_url_pattern": "https://mangadna.com/manga/{manga_id}/{chapter_id}", "name": "MangaDNA"}}, {"id": "mangaeighteenUS", "name": "MangaEighteenUS", "class_name": "GenericProvider", "url": "https://manga18.us", "supports_nsfw": true, "params": {"base_url": "https://manga18.us", "search_url": "https://manga18.us/search", "manga_url_pattern": "https://manga18.us/manga/{manga_id}", "chapter_url_pattern": "https://manga18.us/manga/{manga_id}/{chapter_id}", "name": "MangaEighteenUS"}}, {"id": "viewcomics", "name": "ViewComics", "class_name": "GenericProvider", "url": "https://viewcomics.me", "supports_nsfw": false, "params": {"base_url": "https://viewcomics.me", "search_url": "https://viewcomics.me/search", "manga_url_pattern": "https://viewcomics.me/comic/{manga_id}", "chapter_url_pattern": "https://viewcomics.me/comic/{manga_id}/{chapter_id}", "name": "ViewComics"}}, {"id": "coloredmanga", "name": "ColoredManga", "class_name": "GenericProvider", "url": "https://coloredmanga.com", "supports_nsfw": false, "params": {"base_url": "https://coloredmanga.com", "search_url": "https://coloredmanga.com/search", "manga_url_pattern": "https://coloredmanga.com/manga/{manga_id}", "chapter_url_pattern": "https://coloredmanga.com/manga/{manga_id}/{chapter_id}", "name": "ColoredManga"}}, {"id": "crazyscans", "name": "CrazyScans", "class_name": "GenericProvider", "url": "https://crazyscans.com", "supports_nsfw": false, "params": {"base_url": "https://crazyscans.com", "search_url": "https://crazyscans.com/search", "manga_url_pattern": "https://crazyscans.com/manga/{manga_id}", "chapter_url_pattern": "https://crazyscans.com/manga/{manga_id}/{chapter_id}", "name": "CrazyScans"}}, {"id": "<PERSON><PERSON>gain", "name": "KissmangaIN", "class_name": "GenericProvider", "url": "https://kissmanga.in", "supports_nsfw": false, "params": {"base_url": "https://kissmanga.in", "search_url": "https://kissmanga.in/search", "manga_url_pattern": "https://kissmanga.in/manga/{manga_id}", "chapter_url_pattern": "https://kissmanga.in/manga/{manga_id}/{chapter_id}", "name": "KissmangaIN"}}]