============================= test session starts ==============================
platform linux -- Python 3.13.5, pytest-8.4.1, pluggy-1.6.0 -- /home/<USER>/Apps/kuroibara/backend/venv/bin/python
cachedir: .pytest_cache
rootdir: /home/<USER>/Apps/kuroibara/backend
configfile: pytest.ini
plugins: anyio-4.9.0, cov-6.2.1, asyncio-1.0.0
asyncio: mode=Mode.AUTO, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collecting ... collected 1 item

tests/test_api.py::test_api_docs PASSED                                  [100%]

=============================== warnings summary ===============================
tests/test_api.py::test_api_docs
  /home/<USER>/Apps/kuroibara/backend/app/core/services/provider_monitor.py:149: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).
    now = datetime.utcnow()

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
======================== 1 passed, 1 warning in 40.56s =========================
